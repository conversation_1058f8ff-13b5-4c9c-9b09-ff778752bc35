{"name": "pasteflow", "version": "1.0.0", "main": "main.js", "scripts": {"start": "electron .", "dev": "vite", "dev:electron": "node dev.js", "build": "vite build", "build-electron": "node build.js", "verify-build": "node scripts/verify-build.js", "test-build": "node scripts/test-local-build.js", "test-build:mac": "node scripts/test-local-build.js mac", "test-build:win": "node scripts/test-local-build.js win", "test-build:linux": "node scripts/test-local-build.js linux", "debug-gh-release": "git tag debug-v$(date +'%Y%m%d%H%M%S') && git push origin --tags", "package": "vite build && electron-builder --publish=never", "package:mac": "vite build && electron-builder --mac --publish=never", "package:win": "vite build && electron-builder --win --publish=never", "package:linux": "vite build && electron-builder --linux --publish=never", "package:all": "vite build && electron-builder -mwl --publish=never", "release": "vite build && electron-builder --publish=onTagOrDraft", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives", "lint:strict": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:filenames": "eslint . --ext ts,tsx --rule \"filenames/match-regex: error\" --rule \"filenames/match-exported: error\" --rule \"filenames/no-index: off\"", "rename:check": "node scripts/rename-to-kebab-case.js --dry-run", "rename:files": "node scripts/rename-to-kebab-case.js", "preview": "vite preview", "test": "jest --maxWorkers=2", "test:unit": "jest --testPathPattern='(^((?!integration|e2e).)*test\\.(ts|tsx)$)' --maxWorkers=2", "test:integration": "jest --testPathPattern='integration' --maxWorkers=1", "test:e2e": "jest --testPathPattern='e2e' --maxWorkers=1", "test:ci": "jest --ci --coverage --maxWorkers=2", "test:debug": "node --inspect-brk ./node_modules/.bin/jest --runInBand", "test:failed": "jest --onlyFailures", "test:watch": "jest --watch --maxWorkers=2", "test:mock-check": "npx tsx scripts/test-quality/mock-count-checker.ts", "test:assertion-check": "npx tsx scripts/test-quality/assertion-density-checker.ts", "test:quality-full": "npm run test:mock-check && npm run test:assertion-check && npm test"}, "keywords": ["clipboard", "code", "developer-tools", "electron", "file-viewer"], "author": {"name": "kleneway", "email": "<EMAIL>"}, "license": "MIT", "description": "A modern file viewer application for developers to easily navigate, search, and copy code from repositories.", "build": {"appId": "com.nbailon.pasteflow", "productName": "PasteFlow", "directories": {"output": "release-builds"}, "files": ["dist/**/*", "main.js", "preload.js", "excluded-files.js", "node_modules/**/*"], "mac": {"category": "public.app-category.developer-tools", "icon": "public/favicon.icns", "target": ["dmg", "zip"], "hardenedRuntime": false}, "win": {"target": ["nsis", "portable"], "icon": "public/favicon.ico"}, "linux": {"target": ["AppImage", "deb", "rpm"], "category": "Development", "icon": "public/favicon.png"}, "asarUnpack": ["node_modules/ignore/**", "node_modules/tiktoken/**", "node_modules/gpt-3-encoder/**"], "asar": true, "afterSign": "scripts/notarize.js", "publish": ["github"]}, "devDependencies": {"@electron/notarize": "^2.5.0", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.12", "@types/node": "^20.10.5", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.7", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "electron": "^34.3.0", "electron-builder": "^24.13.3", "eslint": "^8.55.0", "eslint-import-resolver-typescript": "^4.2.7", "eslint-plugin-filenames": "^1.3.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-json": "^4.0.1", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "eslint-plugin-sonarjs": "^0.23.0", "eslint-plugin-unicorn": "^46.0.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jsonc-eslint-parser": "^2.4.0", "prettier": "^2.8.8", "ts-jest": "^29.1.2", "typescript": "^5.3.3", "vite": "^5.0.8", "vite-plugin-top-level-await": "^1.6.0", "vite-plugin-wasm": "^3.5.0"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.6", "@radix-ui/themes": "^3.2.1", "@types/react-modal": "^3.16.3", "@types/react-syntax-highlighter": "^15.5.13", "@types/react-window": "^1.8.8", "@xmldom/xmldom": "^0.9.6", "gpt-3-encoder": "^1.1.4", "ignore": "^7.0.3", "jotai": "^2.12.3", "lucide-react": "^0.477.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-modal": "^3.16.3", "react-syntax-highlighter": "^15.6.1", "react-window": "^1.8.11", "tiktoken": "^1.0.20"}}