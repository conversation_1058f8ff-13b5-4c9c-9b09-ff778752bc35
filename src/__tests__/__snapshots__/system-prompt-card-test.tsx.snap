// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SystemPromptCard Component renders consistent UI with all expected elements 1`] = `
<div>
  <div
    class="file-card system-prompt-card"
  >
    <div
      class="file-card-header"
    >
      <div
        class="file-card-icon"
      >
        <div
          data-size="16"
          data-testid="message-square-code-icon"
        />
      </div>
      <div
        class="file-card-name monospace"
      >
        Test Prompt Name
      </div>
    </div>
    <div
      class="file-card-line-badge system-prompt-badge"
    >
      System Prompt
    </div>
    <div
      class="file-card-info"
    >
      <div
        class="file-card-tokens"
      >
        ~
        20
         tokens
      </div>
    </div>
    <div
      class="file-card-actions"
    >
      <button
        class="file-card-action"
        data-copy-text="This is a test system prompt content with enough text to test token counting."
        data-testid="copy-button"
      />
      <button
        class="file-card-action remove-selection-btn"
        title="Remove from selection"
      >
        <div
          data-size="16"
          data-testid="x-icon"
        />
      </button>
    </div>
  </div>
</div>
`;
