export const STORAGE_KEYS = {
  SELECTED_FOLDER: 'pasteflow.selected_folder',
  SELECTED_FILES: 'pasteflow.selected_files',
  SORT_ORDER: 'pasteflow.sort_order',
  FILE_TREE_SORT_ORDER: 'pasteflow.file_tree_sort_order',
  SEARCH_TERM: 'pasteflow.search_term',
  EXPANDED_NODES: 'pasteflow.expanded_nodes',
  FILE_TREE_MODE: 'pasteflow.file_tree_mode',
  SYSTEM_PROMPTS: 'pasteflow.system_prompts',
  ROLE_PROMPTS: 'pasteflow.role_prompts',
  DOCS: 'pasteflow.docs',
  INSTRUCTIONS: 'pasteflow.instructions',
  WORKSPACES: 'pasteflow.workspaces',
  CURRENT_WORKSPACE: 'pasteflow.current_workspace',
  WORKSPACE_SORT_MODE: 'pasteflow.workspace_sort_mode',
  WORKSPACE_MANUAL_ORDER: 'pasteflow.workspace_manual_order'
};

export const SORT_OPTIONS = [
  { value: "name-asc", label: "Name (A-Z)" },
  { value: "name-desc", label: "Name (Z-A)" },
  { value: "tokens-asc", label: "Tokens (Lowest First)" },
  { value: "tokens-desc", label: "Tokens (Highest First)" },
  { value: "size-asc", label: "Size (Smallest First)" },
  { value: "size-desc", label: "Size (Largest First)" }
];

export const DEFAULT_EXCLUSION_PATTERNS = [
  "**/node_modules/",
  "**/.npm/",
  "**/__pycache__/",
  "**/.pytest_cache/",
  "**/.mypy_cache/",
  "**/.gradle/",
  "**/.nuget/",
  "**/.cargo/",
  "**/.stack-work/",
  "**/.ccache/",
  "**/.idea/",
  "**/.vscode/",
  "**/*.swp",
  "**/*~",
  "**/*.tmp",
  "**/*.temp",
  "**/*.bak",
  "**/*.meta",
  "**/package-lock.json",
]; 