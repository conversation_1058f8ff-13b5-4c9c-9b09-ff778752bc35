/* Import color variables */
@import './colors.css';

/* Radix UI Dialog Styles */

/* Dialog Overlay - background overlay */
.modal-overlay {
  background-color: rgba(0, 0, 0, 0.75);
  position: fixed;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* Dialog Content - the modal container */
.modal-content {
  position: relative;
  background-color: var(--background-primary);
  border-radius: 8px;
  padding: 0;
  border: 1px solid var(--border-color);
  max-width: 90%;
  width: 1000px;
  max-height: 90vh;
  margin: auto;
  overflow: hidden;
  z-index: 1001;
  bottom: 50%;
  transform: translateY(-50%);
}

/* System prompts modal */
.system-prompts-modal,
.role-prompts-modal,
.instructions-modal {
  display: flex;
  flex-direction: column;
  height: 90vh;
  background-color: var(--background-primary);
}

/* Apply changes modal */
.apply-changes-modal {
  width: 80%;
  max-width: 800px;
  max-height: 90vh;
}

/* Filter modal */
.filter-modal {
  width: 80%;
  max-width: 800px;
  max-height: 90vh;
}

/* File view modal */
.file-view-modal {
  position: relative;
  width: 90%;
  max-width: 1200px;
  height: 90vh;
  border-radius: 8px;
  padding: 0;
  margin: auto;
  background-color: var(--background-primary);
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

/* Workspace modal */
.workspace-modal {
  width: 500px;
  max-width: 90%;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

/* Modal Header */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--background-secondary);
}

.modal-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.close-button {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  border-radius: 4px;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-button:hover {
  background-color: var(--hover-color);
  color: var(--text-primary);
}

/* Modal Body */
.modal-body {
  padding: 16px;
  overflow-y: auto;
  flex: 1;
  background-color: var(--background-primary);
}

/* Notes app layout - used by prompts and docs modals */
.notes-app-layout .modal-body {
  display: flex;
  gap: 16px;
  padding: 0;
  overflow: hidden;
  height: calc(100% - 50px); /* Subtract header height */
}

.notes-app-layout .sidebar {
  width: 280px;
  overflow-y: auto;
  border-right: 1px solid var(--border-color);
  padding: 12px;
  background-color: var(--background-secondary);
}

.notes-app-layout .content-area {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  background-color: var(--background-primary);
}

/* Modal Footer */
.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 12px 16px;
  border-top: 1px solid var(--border-color);
  background-color: var(--background-secondary);
}

/* Buttons */
.apply-button,
.cancel-button {
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  font-weight: 500;
  cursor: pointer;
}

.apply-button {
  background-color: var(--primary-button-background);
  color: var(--primary-button-text);
}

.apply-button:hover {
  background-color: var(--accent-blue);
}

.apply-button:disabled {
  background-color: var(--text-disabled);
  cursor: not-allowed;
}

.cancel-button {
  background-color: var(--background-primary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.cancel-button:hover {
  background-color: var(--hover-color);
}

.cancel-button:disabled {
  color: var(--text-disabled);
  cursor: not-allowed;
}

/* Animation for dialog */
@keyframes dialogContentShow {
  from {
    opacity: 0;
    transform: translate(-50%, -48%) scale(0.96);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

@keyframes dialogOverlayShow {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
} 