/* Import Radix UI Dialog styles */
@import './radix-modal.css';
/* Import color variables */
@import './colors.css';

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica,
    Arial, sans-serif;
  margin: 0;
  padding: 0;
  background-color: var(--background-primary);
  color: var(--text-primary);
  line-height: 1.5;
  height: 100vh;
  overflow: hidden;
}

#root {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

button {
  cursor: pointer;
  font-family: inherit;
  border: 0.0625rem solid var(--border-color);
  background-color: var(--background-primary);
  font-size: 0.875rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.375rem 0.75rem;
  height: 2rem;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
  white-space: nowrap;
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--text-primary);
}

/* Ensure icon-only buttons maintain size */
.workspace-button {
  width: 2rem; /* Match height */
  padding: 0; /* Remove padding if only icon */
  gap: 0; /* Remove gap if only icon */
}

button:hover {
  background-color: var(--hover-color);
}

button:focus {
  outline: none;
  outline-offset: 0.0625rem;
}

button.primary {
  background-color: var(--primary-button-background);
  color: var(--primary-button-text);
  border-color: var(--primary-button-background);
}

button.primary:hover {
  background-color: var(--primary-button-background);
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

input[type="text"],
input[type="search"] {
  padding: 0.5rem 0.75rem;
  border: 0.0625rem solid var(--border-color);
  border-radius: 0.25rem;
  font-family: inherit;
  font-size: 0.875rem;
  outline: none;
  width: 100%;
  background-color: var(--background-primary);
  color: var(--text-primary);
}

input[type="text"]:focus,
input[type="search"]:focus {
  border-color: var(--accent-blue);
  box-shadow: 0 0 0 0.0625rem var(--accent-blue);
}

.monospace {
  font-family: Consolas, Menlo, Monaco, "Courier New", monospace;
  line-height: 1.5;
}

/* Main layout structure */
.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.header {
  padding: 1rem 1.5rem;
  border-bottom: 0.0625rem solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--background-primary);
}

.header h1 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-primary);
  display: flex;
  align-items: center;
}

.app-title {
  display: flex;
  align-items: center;
}

.folder-name-container {
  display: flex;
  align-items: center;
  font-weight: 400;
  color: var(--text-secondary);
}

.folder-name {
  margin-left: 0.25rem;
}

.folder-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.selected-folder {
  padding: 0.375rem 0.75rem;
  border-radius: 0.25rem;
  background-color: var(--hover-color);
  max-width: 18.75rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: var(--text-secondary);
}

.select-folder-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--background-secondary);
  border: none;
  border-radius: 0.375rem;
  color: var(--text-primary);
  padding: 0.25rem;
  height: 2rem;
  width: 2rem;
  transition: background-color 0.2s ease;
  border: 0.0625rem solid var(--icon-stroke-color);
  box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}

.select-folder-btn.large {
  width: 4rem;
  height: 4rem;
}

.select-folder-btn svg {
  stroke: var(--icon-stroke-color);
  fill: var(--background-primary);
  stroke-width: 0.09375rem;
}

.select-folder-btn:focus {
  outline-width: 0.03125rem;
  background-color: var(--hover-color);
}

.select-folder-btn:focus svg,
.select-folder-btn:hover svg {
  stroke: var(--icon-hover-stroke-color);
}

.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
  height: 100%;
  flex-direction: row-reverse;
}

.sidebar {
  width: 18.75rem;
  min-width: 12.5rem;
  max-width: 31.25rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  border-left: 0.0625rem solid var(--border-color);
  overflow: hidden;
  background-color: var(--background-secondary);
  position: relative;
  transition: width 0.1s ease;
}

.sidebar-header {
  padding: 1rem;
  border-bottom: 0.0625rem solid var(--border-color);
  background-color: var(--background-secondary);
}

.sidebar-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.sidebar-folder-path {
  font-size: 0.75rem;
  color: var(--text-secondary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.sidebar-search {
  background-color: var(--background-secondary);
}

.sidebar-buttons {
  display: flex;
  align-items: center;
  background-color: var(--background-secondary);
  border-bottom: 0.0625rem solid var(--border-color);
  padding: 0.5rem 1rem;
  gap: 0.5rem;
  height: 3rem;
}

.sidebar-button {
  min-width: 1.75rem;
  height: 1.75rem;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--background-secondary);
  border: 0.0625rem solid var(--icon-stroke-color);
  border-radius: 0.375rem;
  color: var(--text-primary);
  cursor: pointer;
  transition: background-color 0.2s, transform 0.1s;
  box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}

.sort-dropdown-container {
  width: 25%;
  position: relative;
}

.sidebar-buttons > .sidebar-button {
  width: 25%;
}

.sidebar-button:active {
  transform: translateY(0);
}

.sidebar-button:focus {
  outline-width: 0.03125rem;
  background-color: var(--hover-color);
}

.sidebar-button svg {
  stroke: var(--icon-stroke-color);
  fill: var(--background-primary);
  stroke-width: 0.09375rem;
}

.sidebar-button svg path {
  fill: transparent;
}

.sidebar-button:focus svg {
  stroke: var(--icon-hover-stroke-color);
}

.sort-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  background-color: var(--background-color);
  border-radius: 0.375rem;
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

.sort-dropdown-file-tree {
  background-color: var(--background-primary);
}

.sort-dropdown-button {
  display: flex;
  align-items: center;
  font-size: 0.6875rem;
  font-weight: 400;
  text-align: left;
  gap: 0.375rem;
  padding: 0.375rem 0.625rem;
  border-radius: 0.375rem;
  background: none;
  color: var(--text-color);
  cursor: pointer;
}

.sort-dropdown-button svg {
  stroke: var(--icon-stroke-color);
  fill: var(--background-primary);
  stroke-width: 0.09375rem;
}

/* Apply sort-dropdown-button styles to all buttons in sort-dropdown-file-tree */
.sort-dropdown-file-tree button {
  display: flex;
  align-items: center;
  font-size: 0.6875rem;
  font-weight: 400;
  text-align: left;
  padding: 0.5rem 1rem;
  background: none;
  border: none;
  color: var(--text-color);
  cursor: pointer;
}

.sort-dropdown button span {
  margin-right: 0.5rem;
}

.sort-dropdown button .checkmark {
  margin-left: auto;
  margin-right: 0;
  color: var(--option-selected-text);
}

.sidebar-buttons .sidebar-button {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.375rem 0.625rem;
}

.sidebar-actions {
  display: flex;
  padding: 0.75rem 1rem;
  gap: 0.5rem;
  border-bottom: 0.0625rem solid var(--border-color);
  background-color: var(--background-secondary);
}

.sidebar-action-btn {
  flex: 1;
  font-size: 0.8125rem;
  padding: 0.375rem 0.75rem;
  background-color: var(--background-primary);
  color: var(--text-primary);
  border: 0.0625rem solid var(--border-color);
}

.file-tree {
  flex: 1;
  overflow-y: auto;
  padding: 0.5rem 0;
  background-color: var(--background-secondary);
}

.folder-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.5rem;
  flex-shrink: 0;
  color: var(--icon-color);
}

.tree-item-icon svg,
.folder-icon svg {
  stroke: var(--checkbox-border);
}

.folder-path {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.tree-empty {
  padding: 1rem;
  text-align: center;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.tree-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1.5rem 1rem;
  gap: 0.75rem;
  color: var(--text-secondary);
}

.tree-loading .spinner {
  width: 1.5rem;
  height: 1.5rem;
}

.folder-header {
  background-color: var(--background-secondary);
  font-weight: 500;
  position: sticky;
  top: 0;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.folder-header-left {
  display: flex;
  align-items: center;
  overflow: hidden;
}

.folder-select-checkbox {
  margin-right: 0.5rem;
  cursor: pointer;
}

.folder-actions {
  display: flex;
  align-items: center;
  margin-left: auto;
}

.folder-action-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 0.25rem;
  cursor: pointer;
  padding: 0;
  border-radius: 0.25rem;
  transition: background-color 0.2s, color 0.2s;
}

.folder-action-btn:hover {
  background-color: var(--background-tertiary);
  color: var(--text-primary);
}

.folder-action-btn:focus {
  outline: none;
  box-shadow: 0 0 0 0.125rem var(--primary-color-transparent);
}

.folder-action-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  color: var(--text-muted);
}

.folder-action-btn:disabled:hover {
  background: none;
  color: var(--text-muted);
}

.tree-item {
  display: flex;
  align-items: center;
  padding: 0.125rem 0.375rem;
  margin: 0;
  border-radius: 0.25rem;
  align-items: center;
  cursor: pointer;
  user-select: none;
  position: relative;
  transition: background-color 0.1s ease;
  color: var(--text-primary);
}

.tree-item:hover {
  background-color: var(--hover-color);
}

.tree-item.selected {
  background-color: var(--background-selected);
}

.tree-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.5rem;
  flex-shrink: 0;
  color: var(--icon-color);
}

.tree-item-content {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.tree-item-toggle {
  width: 1.25rem;
  height: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.25rem;
  cursor: pointer;
  color: var(--icon-color);
  z-index: 2;
}

.tree-item-toggle svg {
  transition: transform 0.15s ease-in-out;
  transform: rotate(0deg);
}

.tree-item-toggle.expanded svg {
  transform: rotate(90deg);
}

.tree-item-indent {
  width: 1rem;
  flex-shrink: 0;
}

.tree-item-checkbox {
  cursor: pointer;
  position: relative;
  z-index: 1;
  opacity: 0;
}

.tree-item-checkbox-container {
  margin-right: 0.5rem;
  cursor: pointer;
  position: relative;
}

.custom-checkbox {
  display: block;
  width: 0.875rem;
  height: 0.875rem;
  background-color: var(--checkbox-background);
  border: 0.0625rem solid var(--checkbox-border);
  border-radius: 0.21875rem;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

/* Add checked state styling */
.tree-item-checkbox:checked + .custom-checkbox {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.tree-item-checkbox:checked + .custom-checkbox::before {
  content: "";
  position: absolute;
  left: 50%;
  top: calc(50% - 0.0625rem);
  transform: translate(-50%, -50%) rotate(45deg);
  width: 0.25rem;
  height: 0.5625rem;
  border-bottom: 0.0625rem solid var(--text-primary);
  border-right: 0.0625rem solid var(--text-primary);
  z-index: 2;
}

.tree-item-checkbox:indeterminate + .custom-checkbox::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
  height: 0.0625rem;
  background-color: var(--text-primary);
  z-index: 2;
}

/* Add hover state for the checkbox */
.tree-item-checkbox-container:hover .custom-checkbox {
  border-color: var(--primary-color);
}

.tree-item-name {
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 0.78125rem;
  letter-spacing: 0.0125rem;
  color: var(--text-primary);
}

.tree-item-tokens {
  font-size: 0.625rem;
  color: var(--text-secondary);
  margin-left: 0.375rem;
  white-space: nowrap;
}

.tree-item-badge {
  font-size: 0.625rem;
  padding: 0.0625rem 0.3125rem;
  border-radius: 0.25rem;
  background-color: var(--hover-color);
  color: var(--text-secondary);
  margin-left: 0.375rem;
  white-space: nowrap;
}

.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: var(--background-primary);
  min-width: 0;
}

.user-instructions-input-area {
  padding: 1rem 1.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 9.375rem;
}

.instructions-token-count {
  font-size: 0.625rem;
  color: var(--secondary-text-color);
  margin-bottom: 0.25rem;
  text-align: right;
}

.token-count-display {
  font-size: 11px;
  color: var(--text-secondary);
  margin-top: 0.25rem;
  text-align: center;
}

.theme-segment .file-tree-token-count {
  font-size: 0.625rem;
  color: var(--text-secondary);
  margin-left: 0.375rem;
  white-space: nowrap;
}

.user-instructions-input {
  width: 100%;
  height: 100%;
  padding: 1rem;
  border: 0.0625rem solid var(--border-color);
  border-radius: 0.375rem;
  background-color: var(--background-primary);
  color: var(--text-primary);
  font-family: inherit;
  font-size: 0.875rem;
  line-height: 1.5;
  resize: none;
  transition: all 0.2s ease;
  box-shadow: 0 0.0625rem 0.1875rem rgba(0, 0, 0, 0.05);
  outline: none;
}

.selected-files-content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1.5rem;
  border-bottom: 0.0625rem solid var(--border-color);
  background-color: var(--background-primary);
}

.content-title {
  font-size: 0.75rem;
  font-weight: 700;
  color: var(--text-primary);
}

.selected-files-content-area {
  margin-top: auto;
  height: 25vh;
  display: flex;
  flex-direction: column;
}

.content-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.sort-dropdown-selected-files {
  position: relative;
}

.sort-options {
  position: absolute;
  top: 100%;
  left: 0;
  right: auto;
  min-width: 10.75rem;
  background-color: var(--background-primary);
  box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
  z-index: 100;
  width: 100%;
}

.sort-option {
  display: flex;
  font-size: 0.6875rem;
  padding: 0.5rem 1rem;
  gap: 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s;
  color: var(--text-primary);
  width: 100%;
  justify-content: flex-start;
}

.sort-option:hover {
  background-color: var(--hover-color);
}

.sort-option.active {
  background-color: var(--option-selected-bg);
  font-weight: 600;
  color: var(--option-selected-text);
}

.file-stats {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.file-list-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.file-list {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  gap: 0.75rem;
  background-color: var(--background-primary);
}

.file-list-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-secondary);
  font-size: 1rem;
  padding: 2rem;
  text-align: center;
}

.file-card {
  display: flex;
  flex-direction: row;
  padding: 0.25rem;
  border: 0.0625rem solid var(--border-color);
  border-radius: 0.25rem;
  position: relative;
  transition: all 0.2s ease;
  background-color: var(--background-primary);
  width: auto;
  max-width: fit-content;
}

.file-card:hover {
  background-color: var(--hover-color);
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.05);
}

.file-card.selected {
  border: 0.125rem solid var(--file-card-selected-border);
  background-color: var(--background-selected);
}

.dark-mode .file-card.selected {
  border: 0.125rem solid var(--file-card-selected-border);
}

.file-card-header {
  display: flex;
  align-items: center;
}

.file-card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.25rem;
  color: var(--icon-color);
  flex-shrink: 0;
}

.file-card-name {
  font-size: 0.625rem;
  line-height: 1rem;
  font-weight: 500;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: var(--text-primary);
  max-width: 12.5rem;
  letter-spacing: -0.0125rem;
  position: relative;
  top: 1px;
}

.file-card-info {
  display: flex ;
  flex-direction: column;
  margin-bottom: 0;
  align-items: center;
  justify-content: center;
  margin-left: .5rem;
}

.file-card-line-badge {
  font-size: 0.625rem;
  color: var(--text-primary);
  margin-left: .5rem;
  padding: 0 0.375rem;
  background-color: var(--background-selected);
  border-radius: 4px;
  display: inline-block;
  font-weight: 500;
  text-align: center;
}

.file-card-tokens {
  color: var(--text-secondary);
  font-size: 0.625rem;
  text-align: right;
}

.file-card-status {
  font-size: 0.8125rem;
  color: var(--text-secondary);
}

.file-card-actions {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.85);
  display: flex;
  gap: 0.25rem;
  opacity: 0;
  transition: opacity 0.2s ease;
  align-items: anchor-center;
  justify-content: end;
  padding-right: .25rem;
}

.file-card:hover .file-card-actions {
  opacity: 1;
}

.file-card-action {
  width: 1.5rem;
  height: 1.5rem;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--background-primary);
  border: 0.0625rem solid var(--border-color);
  border-radius: 0.25rem;
  color: var(--icon-color);
  transition: all 0.15s ease;
  box-shadow: 0 0.0625rem 0.1875rem rgba(0, 0, 0, 0.05);
}

.file-card-action:hover {
  background-color: var(--icon-color);
  color: var(--background-primary);
  border-color: var(--background-primary);
  transform: translateY(-0.0625rem);
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.1);
}

.file-card-action:active {
  transform: translateY(0);
  box-shadow: 0 0.0625rem 0.125rem rgba(0, 0, 0, 0.1);
}

.copy-button-container {
  display: flex;
  flex-direction: row;
  align-items: top;
  gap: 1rem;
  justify-content: center;
  background-color: var(--background-primary);
  margin-top: auto;
  padding: 1rem;
}

.copy-button-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 25rem;
}

.copy-button-container .copy-button {
  width: 100%;
}

.file-tree-format-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.file-tree-options-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.copy-selected-files-btn {
  padding: 0.625rem 1rem;
}

.copy-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border-radius: 0.375rem;
  background-color: var(--background-secondary);
  color: var(--text-primary);
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s, transform 0.1s;
  border: 0.0625rem solid var(--border-color);
}

.copy-button.primary {
  background-color: var(--primary-button-background);
  color: var(--primary-button-text);
  border: none;
}

.copy-button.primary:hover:not(:disabled) {
  background-color: var(--primary-button-background);
}

.copy-button.full-width {
  width: 100%;
  max-width: 25rem;
}

.copy-button.copied {
  background-color: var(--success-color) !important;
  border-color: var(--success-color) !important;
  color: white !important;
  transition: all 0.2s ease;
  animation: flash-success 0.3s;
}

@keyframes flash-success {
  0% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.9;
  }
}

.copy-button-text {
  font-size: 14px;
  letter-spacing: 0.5px;
}

.copy-status {
  opacity: 0;
  transition: opacity 0.3s ease;
  color: var(--success-color);
  font-weight: 500;
}

.copy-status.visible {
  opacity: 1;
}

.processing-indicator {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.processing-indicator .content {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: var(--background-secondary);
  padding: 1.25rem;
  border-radius: 8px;
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.2);
  max-width: 20rem;
  width: auto;
}

/* Add styling for the cancel button in processing indicator */
.processing-indicator .cancel-button {
  margin-top: 0.75rem;
  padding: 0.375rem 0.75rem;
  background-color: var(--error-color, #dc3545);
  color: white;
  border: none;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.processing-indicator .cancel-button:hover {
  background-color: var(--error-hover-color, #bd2130);
}

.spinner {
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 3px solid var(--accent-blue);
  width: 24px;
  height: 24px;
  animation: spin 1s linear infinite;
}

.progress-bar-container {
  width: 100%;
  height: 10px;
  background-color: var(--background-primary);
  border-radius: 5px;
  overflow: hidden;
  margin-top: 10px;
  position: relative;
  max-width: 18rem;
}

.progress-bar {
  height: 100%;
  background-color: var(--accent-blue);
  border-radius: 5px;
  transition: width 0.3s ease;
}

.progress-details {
  display: block;
  text-align: center;
  margin-top: 8px;
  margin-bottom: 8px;
  font-size: 12px;
  color: var(--text-primary);
  max-width: 18rem;
  word-wrap: break-word;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.error-message {
  background-color: rgba(231, 76, 60, 0.1);
  color: var(--error-color);
  padding: 15px;
  margin: 15px;
  border-radius: 4px;
  border-left: 4px solid var(--error-color);
  font-size: 14px;
  line-height: 1.5;
}

/* Welcome Screen Styles */
.welcome-screen {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  height: calc(100vh - 60px);
  overflow: hidden;
  animation: fadeIn 1s ease-in-out;
}

.ascii-logo {
  font-family: monospace;
  font-size: 0.7rem;
  line-height: 1.2;
  white-space: pre;
  text-align: center;
  color: var(--text-secondary);
  transform-origin: center;
  background: var(--background-primary);
  padding: 1.5rem;
  border-radius: 0.5rem;
  position: relative;
}

.welcome-message {
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
}

.welcome-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--background-secondary);
  border: none;
  border-radius: 0.5rem;
  color: var(--text-primary);
  padding: 0.5rem;
  height: 4rem;
  width: 4rem;
  transition: background-color 0.2s ease;
  border: 0.0625rem solid var(--icon-stroke-color);
  box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.welcome-button:hover {
  background-color: var(--hover-color);
  transform: translateY(-0.125rem);
  box-shadow: 0 0.375rem 0.75rem rgba(0, 0, 0, 0.15);
}

.welcome-button:active {
  transform: translateY(0);
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.1);
}

.welcome-button svg {
  stroke: var(--icon-stroke-color);
  fill: var(--background-primary);
  stroke-width: 0.09375rem;
  width: 3rem;
  height: 3rem;
}

.welcome-button:hover svg {
  stroke: var(--icon-hover-stroke-color);
}

@keyframes pulsate {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1.03);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(1.25rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments for smaller screens */
@media (max-width: 48rem) {
  .ascii-logo {
    font-size: 0.5rem;
  }
  
  .welcome-message h2 {
    font-size: 1.8rem;
  }
  
  .welcome-message p {
    font-size: 1rem;
  }
}

.search-bar {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  background-color: var(--background-primary);
  border-radius: 0.25rem;
  box-shadow: 0 0.0625rem 0.125rem rgba(0, 0, 0, 0.05);
  transition: border-color 0.2s, box-shadow 0.2s;
}

.search-icon {
  position: absolute;
  left: 0.625rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--icon-color);
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
  z-index: 2;
  width: 1rem;
  height: 1rem;
}

.search-bar .search-input,
input[type="search"].search-input,
input[type="text"].search-input {
  width: 100%;
  padding: 0.5rem 2rem 0.5rem 2.25rem !important;
  border: none;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  line-height: 1.75;
  outline: none;
  background-color: transparent;
  color: var(--text-primary);
}

.search-bar .search-input:focus,
input[type="search"].search-input:focus,
input[type="text"].search-input:focus {
  box-shadow: none;
}

.search-clear-btn {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  padding: 0.25rem;
  color: var(--icon-color);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  cursor: pointer;
  z-index: 2;
}

.search-clear-btn:hover {
  background-color: var(--hover-color);
}

.sidebar-resize-handle {
  position: absolute;
  top: 0;
  left: -0.3125rem;
  width: 0.625rem;
  height: 100%;
  cursor: col-resize;
  padding: 0;
  border: 0;
  width: 6px;
  z-index: 10;
  opacity: 0;
}

.sidebar-resize-handle:hover,
.sidebar-resize-handle:active {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.1);
}

.selected-folder {
  font-size: 0.875rem;
  color: var(--text-secondary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 31.25rem;
  display: inline-block;
}

/* macOS-style segmented control for theme toggle */
.theme-segmented-control {
  display: flex;
  background-color: var(--background-secondary);
  border-radius: 0.375rem;
  padding: 0.125rem;
  box-shadow: 0 0.0625rem 0.1875rem rgba(0, 0, 0, 0.1);
  width: fit-content;
  position: relative;
  height: 2rem;
  overflow: hidden;
  margin-left: auto;
}

.theme-segment {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0;
  padding: 0 0.75rem;
  height: 1.75rem;
  border: none;
  background: none;
  font-size: 0.8125rem;
  font-weight: 500;
  color: var(--text-secondary);
  position: relative;
  z-index: 1;
  transition: color 0.2s ease;
  border-radius: 0.25rem;
  cursor: pointer;
  white-space: nowrap;
  min-width: 4.375rem;
}

.theme-segment:focus {
  outline: none;
}

.theme-segment span {
  font-size: 0.6875rem;
}

.theme-segment.active {
  color: var(--text-primary);
  background-color: var(--background-primary);
  box-shadow: 0 0.0625rem 0.125rem rgba(0, 0, 0, 0.1);
}

/* For dark mode, adjust active segment appearance */
.dark-mode .theme-segment.active {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Animation for segment transition */
.theme-segment {
  transition: background-color 0.2s ease, color 0.2s ease, box-shadow 0.2s ease;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
  width: 100%;
}

/* Apply Changes Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: var(--background-primary);
  border-radius: 0.5rem;
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.15);
  max-width: 50rem;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.apply-changes-modal {
  min-height: 25rem;
}

.filter-modal {
  min-height: 25rem;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.25rem;
  border-bottom: 0.0625rem solid var(--border-color);
}

.modal-header h2 {
  margin: 0;
  font-size: 1.25rem;
  color: var(--text-primary);
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-primary);
  opacity: 0.7;
}

.close-button:hover {
  opacity: 1;
}

.modal-body {
  padding: 1.25rem;
  overflow-y: auto;
  flex: 1;
}

.modal-description {
  margin-bottom: 1rem;
  color: var(--text-primary);
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 0.625rem;
}

.help-toggle-button {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  margin-left: auto;
  background-color: var(--background-secondary);
  border: 0.0625rem solid var(--border-color);
  border-radius: 0.25rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.help-toggle-button:hover {
  background-color: var(--hover-color);
}

.help-text {
  margin-bottom: 1rem;
  padding: 0.75rem;
  background-color: var(--background-secondary);
  border: 0.0625rem solid var(--border-color);
  border-radius: 0.25rem;
  font-size: 0.8125rem;
}

.help-text h3 {
  margin-top: 0;
  margin-bottom: 0.5rem;
}

.help-text pre {
  background-color: var(--background-primary);
  border: 0.0625rem solid var(--border-color);
  border-radius: 0.25rem;
  padding: 0.5rem;
  overflow-x: auto;
  font-size: 0.75rem;
  line-height: 1.4;
  margin-bottom: 0.5rem;
}

.help-text p {
  margin: 0.5rem 0 0;
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.help-text a {
  display: inline-block;
  margin-top: 0.5rem;
  color: var(--accent-blue);
  text-decoration: none;
  font-size: 0.75rem;
}

.help-text a:hover {
  text-decoration: underline;
}

.xml-input {
  width: 100%;
  padding: 0.75rem;
  border: 0.0625rem solid var(--border-color);
  border-radius: 0.25rem;
  background-color: var(--background-secondary);
  color: var(--text-primary);
  font-family: monospace;
  resize: vertical;
  min-height: 12.5rem;
}

.status-message {
  margin-top: 1rem;
  padding: 0.75rem;
  border-radius: 0.25rem;
  background-color: var(--background-secondary);
  white-space: pre-line;
  max-height: 18.75rem;
  overflow-y: auto;
  font-family: monospace;
  line-height: 1.5;
  word-break: break-word;
  border-left: 0.25rem solid var(--border-color);
}

.status-message.error {
  background-color: rgba(255, 0, 0, 0.1);
  color: #ff5252;
  border-left: 0.25rem solid #ff5252;
}

.status-message.success {
  background-color: rgba(0, 255, 0, 0.1);
  color: #4caf50;
  border-left: 0.25rem solid #4caf50;
}

/* File paths in status message */
.status-message ul {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
  padding-left: 1.25rem;
}

.status-message li {
  margin-bottom: 0.25rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 1rem 1.25rem;
  border-top: 0.0625rem solid var(--border-color);
  gap: 0.75rem;
}

.apply-button, .cancel-button {
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.apply-button {
  background-color: var(--primary-button-background);
  color: var(--primary-button-text);
  border: none;
}

.apply-button:hover:not(:disabled) {
  background-color: var(--primary-button-background);
}

.apply-button:disabled {
  background-color: var(--text-disabled);
  cursor: not-allowed;
  opacity: 0.7;
}

.cancel-button {
  background-color: transparent;
  color: var(--text-primary);
  border: 0.0625rem solid var(--border-color);
}

.cancel-button:hover:not(:disabled) {
  background-color: var(--hover-color);
}

.cancel-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Apply XML Changes button in header */
.apply-changes-btn {
  display: flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  background-color: var(--primary-button-background);
  color: var(--primary-button-text);
  border: none;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: background-color 0.2s;
  width: 100%;
  height: 32px;
}

.apply-changes-btn:hover {
  background-color: var(--primary-button-background);
}

.documentation-link {
  color: var(--accent-blue);
  text-decoration: none;
  font-size: 0.75rem;
}

.tree-loading,
.empty-tree,
.empty-list {
  padding: 1rem;
  text-align: center;
  color: var(--text-secondary);
  font-style: italic;
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 0.5rem;
  height: 0.5rem;
}

::-webkit-scrollbar-track {
  background: var(--scrollbar-track);
  border-radius: 0.25rem;
}

::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb);
  border-radius: 0.25rem;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover);
}

.folder-icon-app-title {
  color: var(--text-secondary);
  margin-right: 0.25rem;
}

/* Accessibility Utilities */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

/* File View Modal Styles */
/* This is now handled by the .modal-overlay class in radix-modal.css
.file-view-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}
*/

/* Custom cursor for drag selection */
.file-view-modal-content.selection-active pre span[style*="cursor: pointer"] {
  cursor: text !important; /* Better cursor for selection */
}

.file-view-modal {
  width: 85%;
  max-width: 1200px;
  height: 85%;
  max-height: 800px;
  background-color: var(--background-primary);
  border-radius: 8px;
  padding: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.file-view-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--background-secondary);
}

.file-view-modal-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 300px;
}

.file-view-modal-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--background-secondary);
}

.file-view-modal-selection-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-view-modal-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.selection-mode-radio {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-right: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid var(--border-color);
  background-color: var(--background-primary);
}

.selection-mode-radio label {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  cursor: pointer;
}

.selection-mode-radio input[type="radio"] {
  margin: 0;
}

.file-view-modal-action-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 4px;
  border: 1px solid var(--border-color);
  background: transparent;
  color: var(--text-primary);
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.file-view-modal-action-btn span {
  display: none;
}

@media (min-width: 768px) {
  .file-view-modal-action-btn span {
    display: inline;
  }
}

.file-view-modal-action-btn:hover {
  background-color: var(--hover-color);
}

.file-view-modal-action-btn.active {
  background-color: var(--background-selected);
  color: var(--text-primary);
}

.file-view-modal-action-btn.primary {
  background-color: var(--primary-button-background);
  color: var(--primary-button-text);
  border-color: var(--primary-button-background);
}

.file-view-modal-action-btn.primary:hover {
  background-color: var(--accent-blue);
}

.file-view-modal-close-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  border: none;
  background: transparent;
  color: var(--text-primary);
  cursor: pointer;
  margin-left: .5rem;
}

.file-view-modal-close-btn:hover {
  background-color: var(--hover-color);
}

.file-view-modal-selection-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  font-size: 14px;
  color: var(--text-secondary);
  border-bottom: 1px solid var(--border-color);
  background-color: var(--background-primary);
}

.file-view-modal-entire-file {
  margin-left: 8px;
  font-style: italic;
}

.file-view-modal-content {
  flex: 1;
  overflow: auto;
  position: relative;
}

.file-view-modal-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  font-size: 16px;
  color: var(--text-secondary);
}

.file-view-modal-footer {
  padding: 12px 16px;
  border-top: 1px solid var(--border-color);
  background-color: var(--background-primary);
  font-size: 13px;
  color: var(--text-secondary);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.file-view-modal-buttons {
  display: flex;
  gap: 12px;
  margin-left: auto;
}

.file-view-modal-btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.file-view-modal-btn.cancel {
  background-color: transparent;
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.file-view-modal-btn.cancel:hover {
  background-color: var(--hover-color);
}

.file-view-modal-btn.apply {
  background-color: var(--primary-button-background);
  color: var(--primary-button-text);
  border: none;
}

.file-view-modal-btn.apply:hover {
  background-color: var(--accent-blue);
}

/* Line number and selection styles */
.line-number {
  user-select: none;
}

.line-number.selected {
  font-weight: bold;
}

.line-number.selectable {
  cursor: pointer;
}

.line-number.selectable:hover {
  text-decoration: underline;
}

/* Partial selection indicator in tree view */
.tree-item.partially-selected .tree-item-name {
  font-style: italic;
}

.partial-selection-indicator {
  display: flex;
  margin-left: 6px;
  font-size: 12px;
  color: var(--text-secondary);
  font-style: italic;
}

/* Make file names clickable */
.tree-item-name.clickable {
  cursor: pointer;
  text-decoration: underline;
}
/* 
.tree-item-name.clickable:hover:after {
  content: "";
  position: absolute;
  top: 0;
  left: 1px;
  right: 1px;
  width: auto;
  height: 100%;
  outline: 1.5px solid var(--text-primary);
  border-radius: 4px;
} */

/* File view button */
.tree-item-view-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: transparent;
  border: none;
  border-radius: 4px;
  color: var(--text-secondary);
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.tree-item:hover .tree-item-view-btn {
  opacity: 1;
}

.tree-item-view-btn:hover {
  background-color: var(--hover-color);
  color: var(--accent-blue);
}

/* Line info in file card */
.file-card-lines {
  font-size: 0.6875rem;
  color: var(--text-secondary);
  margin-top: 4px;
  font-style: italic;
}

.token-estimate {
  font-size: 13px;
  color: var(--text-secondary);
}

.selection-help {
  font-size: 13px;
  color: var(--text-secondary);
  font-style: italic;
}

/* System Prompt Card Styling */
.system-prompt-card {
  border-color: #6a3ecf;
  background-color: rgba(106, 62, 207, 0.05);
}

.dark-theme .system-prompt-card {
  background-color: rgba(143, 107, 224, 0.1);
  border-color: #8f6be0;
}

.system-prompt-badge {
  background-color: #6a3ecf;
  color: white;
}

.dark-theme .system-prompt-badge {
  background-color: #8f6be0;
}

/* Role Prompt Card Styling */
.role-prompt-card {
  border-color: #e67e22;
  background-color: rgba(230, 126, 34, 0.05);
}

.dark-theme .role-prompt-card {
  background-color: rgba(230, 126, 34, 0.1);
  border-color: #f39c12;
}

.role-prompt-badge {
  background-color: #e67e22;
  color: white;
}

.dark-theme .role-prompt-badge {
  background-color: #f39c12;
}

/* Selected state for system prompts in the modal */
.system-prompt-item.selected {
  background-color: rgba(106, 62, 207, 0.1);
  border-left: 3px solid #6a3ecf;
}

.dark-theme .system-prompt-item.selected {
  background-color: rgba(143, 107, 224, 0.15);
  border-left: 3px solid #8f6be0;
}

/* Selected state for role prompts in the modal */
.role-prompt-item.selected {
  background-color: rgba(230, 126, 34, 0.1);
  border-left: 3px solid #e67e22;
}

.dark-theme .role-prompt-item.selected {
  background-color: rgba(230, 126, 34, 0.15);
  border-left: 3px solid #f39c12;
}

/* Instruction Card Styling */
.instruction-card {
  border-color: #2980b9;
  background-color: rgba(41, 128, 185, 0.05);
}

.dark-theme .instruction-card {
  background-color: rgba(52, 152, 219, 0.1);
  border-color: #3498db;
}

.instruction-badge {
  background-color: #2980b9;
  color: white;
}

.dark-theme .instruction-badge {
  background-color: #3498db;
}

/* Selected state for instructions in the modal */
.instruction-item.selected {
  background-color: rgba(41, 128, 185, 0.1);
  border-left: 3px solid #2980b9;
}

.dark-theme .instruction-item.selected {
  background-color: rgba(52, 152, 219, 0.15);
  border-left: 3px solid #3498db;
}

.toggle-selection-button.selected {
  color: white;
}

/* .dark-theme .toggle-selection-button.selected {
  outline: 1px solid var(--option-selected-bg);
} */

/* Prompts Buttons and Modals */
.prompts-buttons-container {
  display: flex;
  gap: 0.5rem;
}

.system-prompts-button, .role-prompts-button, .docs-button {
  display: flex;
  align-items: center;
  gap: .25rem;
  padding: 0.375rem 0.75rem;
  background-color: var(--background-secondary);
  border: none;
  border-radius: 0.25rem;
  color: var(--text-primary);
  cursor: pointer;
  transition: background-color 0.2s;
  height: 2rem;
}

.system-prompts-button:hover, .role-prompts-button:hover, .docs-button:hover {
  background-color: var(--hover-color);
}

.role-prompts-button {
  background-color: rgba(230, 126, 34, 0.1);
}

.docs-button {
  background-color: rgba(41, 128, 185, 0.1);
}

.system-prompts-button:hover {
  background-color: var(--background-tertiary);
}

.selected-prompt-indicator {
  font-weight: 500;
  color: var(--accent-color);
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: .15rem;
}

.selected-prompt-indicator svg {
  position: relative;
  top: 1px;
  color: var(--success-color);
}

/* System Prompts Modal */
.system-prompts-modal .modal-body,
.role-prompts-modal .modal-body,
.instructions-modal .modal-body {
  display: flex;
  flex-direction: row;
  padding: 0;
  max-height: calc(90vh - 60px);
  overflow: hidden;
}

.system-prompts-list,
.role-prompts-list,
.instructions-list {
  border: none;
  border-right: 1px solid var(--border-color);
  border-radius: 0;
  margin-bottom: 0;
  max-height: none;
  overflow-y: auto;
  width: 260px;
  background-color: var(--background-secondary);
  flex-shrink: 0;
}

.sidebar.system-prompts-list,
.sidebar.role-prompts-list,
.sidebar.instructions-list {
  padding: 8px 0;
}

.no-prompts-message {
  padding: 32px 16px;
  text-align: center;
  color: var(--text-secondary);
  font-size: 14px;
  background-color: var(--background-secondary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  margin-top: 12px;
}

.system-prompt-item,
.role-prompt-item,
.instruction-item {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 8px 12px;
  border-bottom: none;
  border-radius: 4px;
  margin: 0 4px 8px 4px;
  position: relative;
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.system-prompt-item:hover,
.role-prompt-item:hover,
.instruction-item:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

.system-prompt-item.selected,
.role-prompt-item.selected,
.instruction-item.selected {
  background-color: var(--background-selected);
}

.prompt-details {
  flex: 1;
  overflow: hidden;
}

.prompt-title {
  font-weight: 500;
  margin-bottom: 4px;
  color: var(--text-primary);
  font-size: 13px;
}

.prompt-preview {
  font-size: 12px;
  color: var(--text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.3;
}

.prompt-actions {
  display: none;
  position: absolute;
  right: 0.5rem;
  top: 0.375rem;
  gap: 0.25rem;
}

.prompt-actions button {
  height: 1.375rem;
}

.system-prompt-item:hover .prompt-actions,
.role-prompt-item:hover .prompt-actions,
.instruction-item:hover .prompt-actions {
  display: flex;
}

.prompt-action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  border-radius: 4px;
  border: none;
  background-color: transparent;
  color: var(--icon-color);
  cursor: pointer;
  transition: background-color 0.2s;
}

.prompt-action-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.toggle-selection-button.selected {
  color: var(--success-color);
}

.delete-button:hover {
  color: var(--error-color);
}

.content-area.system-prompt-editor,
.content-area.role-prompt-editor,
.content-area.instruction-editor {
  padding: 2rem;
  background-color: var(--background-primary);
  border-radius: 0;
  flex: 1;
  overflow-y: auto;
}

.add-prompt-form {
  height: 100%;
}

.edit-prompt-form h3,
.add-prompt-form h3 {
  font-size: 16px;
  font-weight: 500;
  margin-top: 0;
  margin-bottom: 16px;
  color: var(--text-primary);
}

.prompt-title-input, 
.prompt-content-input {
  width: 100%;
  padding: 1rem;
  margin-bottom: 1rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: white;
  color: var(--text-primary);
  font-family: inherit;
}

.prompt-content-input {
  width: 100%;
  height: 100%;
  padding: 1rem;
  border: 0.0625rem solid var(--border-color);
  border-radius: 0.375rem;
  background-color: var(--background-primary);
  color: var(--text-primary);
  font-family: inherit;
  font-size: 0.875rem;
  line-height: 1.5;
  resize: vertical;
  height: calc(100% - 6rem);
  transition: all 0.2s ease;
  box-shadow: 0 0.0625rem 0.1875rem rgba(0, 0, 0, 0.05);
  outline: none;
}

.prompt-edit-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.prompt-add-action {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.prompt-add-action  h3 {
  margin: 0;
}

.add-prompt-button {
  display: flex;
  align-items: center;
  gap: .5rem;
  padding: 0.375rem 0.75rem;
  background-color: var(--background-secondary);
  border: none;
  border-radius: 0.25rem;
  color: var(--text-primary);
  cursor: pointer;
  transition: background-color 0.2s;
  height: 2rem;
  margin-left: auto;
}

.notes-app-layout {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.notes-app-layout .modal-header {
  border-bottom: 1px solid var(--border-color);
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--background-secondary);
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.notes-app-layout .modal-header h2 {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin: 0;
}

.system-prompts-list,
.role-prompts-list,
.instructions-list {
  border: none;
  border-right: 1px solid var(--border-color);
  border-radius: 0;
  margin-bottom: 0;
  max-height: none;
  overflow-y: auto;
  width: 260px;
  background-color: var(--background-secondary);
  flex-shrink: 0;
}

/* Workspace Modal Styles */
.workspace-form {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.workspace-subtitle {
  font-size: 14px;
  font-weight: 600;
  margin: 0;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.workspace-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
  max-height: 400px;
  overflow-y: auto;
  padding: 4px;
  background-color: var(--background-secondary);
  border-radius: 6px;
  border: 1px solid var(--border-color);
}

.workspace-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 12px;
  border-radius: 4px;
  background-color: var(--background-primary);
  transition: all 0.15s ease;
  border: 1px solid transparent;
  position: relative;
}

.workspace-item.draggable {
  cursor: grab;
}

.workspace-item.draggable:hover {
  background-color: var(--bg-tertiary);
}

.workspace-item.dragging {
  opacity: var(--workspace-drag-opacity);
  cursor: grabbing;
  z-index: 1000;
}

.workspace-item.drag-over {
  position: relative;
}

.workspace-item.drag-over::before {
  content: '';
  position: absolute;
  top: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--accent-primary);
  box-shadow: 0 0 4px var(--accent-primary);
  animation: pulse 0.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

.drag-handle {
  display: flex;
  align-items: center;
  color: var(--text-secondary);
  opacity: 0.6;
  margin-right: 8px;
  cursor: grab;
  user-select: none;
}

.drag-handle:hover {
  opacity: 1;
}

.workspace-item.dragging .drag-handle {
  cursor: grabbing;
}

.workspace-item:hover {
  background-color: var(--hover-color);
  border-color: var(--border-color);
}

.workspace-actions {
  display: flex;
  gap: 4px;
  align-items: center;
}

.workspace-item .prompt-details {
  flex: 1;
  min-width: 0;
}

.workspace-item .prompt-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Save button animation states */
.save-button {
  position: relative;
  overflow: hidden;
  transition: all 0.2s ease;
}

.save-button.save-saving {
  background-color: var(--accent-blue);
  pointer-events: none;
}

.save-button.save-success {
  background-color: var(--success-color);
  pointer-events: none;
}

.save-button .button-text {
  transition: opacity 0.2s ease;
}

.save-button .button-text.hide {
  opacity: 0;
}

.save-button .button-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.save-button .button-icon.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: translate(-50%, -50%) rotate(0deg); }
  to { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Workspace action buttons */
.workspace-actions .prompt-action-button {
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 500;
  height: 28px;
  min-width: auto;
}

.workspace-actions .prompt-action-button:first-child {
  background-color: var(--primary-button-background);
  color: var(--primary-button-text);
  padding: 4px 12px;
}

.workspace-actions .prompt-action-button:first-child:hover {
  background-color: var(--accent-blue);
}

.workspace-actions .rename-button,
.workspace-actions .delete-button {
  padding: 4px;
  min-width: 28px;
}

.workspace-actions .confirm-button {
  color: var(--success-color);
}

.workspace-actions .cancel-button {
  color: var(--error-color);
}

/* Rename input within workspace item */
.workspace-item .prompt-title-input {
  background-color: var(--background-primary);
  border: 1px solid var(--accent-blue);
  padding: 4px 8px;
  font-size: 14px;
  margin: 0;
}

/* Workspace checkbox styles */
.workspace-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  margin-top: 20px;
}

.workspace-header-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.workspace-sort-selector {
  display: flex;
  align-items: center;
}

.workspace-sort-dropdown {
  padding: 4px 8px;
  font-size: 12px;
  border: 1px solid var(--border-secondary);
  border-radius: 4px;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.workspace-sort-dropdown:hover {
  border-color: var(--border-primary);
}

.workspace-sort-dropdown:focus {
  outline: none;
  border-color: var(--accent-primary);
}

.workspace-select-all {
  display: flex;
  align-items: center;
  gap: 8px;
}

.select-all-label {
  font-size: 13px;
  color: var(--text-secondary);
  cursor: pointer;
  user-select: none;
}

.workspace-select-all .workspace-checkbox-container {
  margin-right: 8px;
}

.workspace-checkbox-container {
  margin-right: 12px;
  display: flex;
  align-items: center;
  position: relative;
  width: 14px;
  height: 14px;
}

/* Bulk actions bar */
.bulk-actions-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  background-color: var(--background-tertiary);
  border-radius: 4px;
  margin-bottom: 12px;
  border: 1px solid var(--border-color);
}

.selected-count {
  font-size: 13px;
  color: var(--text-secondary);
  font-weight: 500;
}

.bulk-actions {
  display: flex;
  gap: 8px;
}

.bulk-action-button {
  padding: 5px 12px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid var(--border-color);
  background-color: var(--background-primary);
  color: var(--text-primary);
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.15s ease;
}

.bulk-action-button:hover {
  background-color: var(--hover-color);
  border-color: var(--accent-blue);
}

.bulk-action-button.delete {
  color: var(--error-color);
  border-color: var(--error-color);
}

.bulk-action-button.delete:hover {
  background-color: var(--error-color);
  color: white;
}

/* Success check animation */
.success-check {
  animation: checkmark 0.3s ease-in-out;
}

@keyframes checkmark {
  0% {
    transform: translate(-50%, -50%) scale(0);
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
  }
}

.workspace-dropdown {
  position: relative;
  display: inline-flex;
  align-items: center;
  cursor: pointer;
}

/* Ensure workspace dropdown menu appears above other dropdowns */
.workspace-dropdown .dropdown-menu {
  z-index: 1100;
}

.dropdown-button,
.dropdown-header {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--text-secondary);
  transition: color 0.2s;
  padding: 0.25rem 0.375rem;
  border-radius: 0.375rem;
  font-size: 0.6875rem;
  background-color: var(--background-secondary);
  border: 0.0625rem solid var(--border-color);
  cursor: pointer;
}

.dropdown-button:hover,
.dropdown-header:hover {
  color: var(--text-primary);
  background-color: var(--hover-color);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  min-width: 12rem;
  background-color: var(--background-primary);
  border: 0.0625rem solid var(--border-color);
  border-radius: 0.375rem;
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.15);
  z-index: 1000;
  margin-top: 0.25rem;
}

.dropdown-item {
  padding: 0.5rem 1rem;
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: background-color 0.2s;
}

.dropdown-item:hover {
  background-color: var(--hover-color);
}

.dropdown-divider {
  height: 0.0625rem;
  margin: 0.5rem 0;
  background-color: var(--border-color);
}

/* XML Editor container */
.xml-editor-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem;
}

/* Make textarea styling consistent with prompts modal */
.xml-input.prompt-content-input {
  font-family: monospace;
  resize: vertical;
  min-height: 12.5rem;
}
