# Contributing to PasteFlow

Thank you for considering contributing to PasteFlow! This document outlines the process for contributing to the project.

## Getting Started

1. Fork the repository
2. Clone your fork: `git clone https://github.com/yourusername/pasteflow.git`
3. Create a branch for your changes: `git checkout -b feature/your-feature-name`
4. Install dependencies: `npm install`
5. Make your changes
6. Run tests and linting: `npm run lint`
7. Commit your changes with a descriptive message

## Pull Request Process

1. Update the README.md or documentation with details of your changes if appropriate
2. Make sure your code passes all linting checks
3. Submit a pull request to the main repository
4. The maintainers will review your PR as soon as possible

## Code Style

- Follow the existing code style in the project
- Run `npm run lint` to ensure your code meets the project's style guidelines
- Comment your code where appropriate

## Issues

- Use GitHub Issues to report bugs or suggest new features
- Check existing issues before creating a new one
- When reporting bugs, include steps to reproduce, expected behavior, and actual behavior

## License

By contributing to PasteFlow, you agree that your contributions will be licensed under the project's MIT License.
